"""
Project-related models for Task Management System
"""

from app.helpers.extensions import db
from datetime import datetime


class Project(db.Model):
    """
    Project model for project management
    """
    __tablename__ = 'project'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    status = db.Column(db.String(50), default='Active')
    priority = db.Column(db.String(50), default='Medium')
    start_date = db.Column(db.Date, nullable=True)
    end_date = db.Column(db.Date, nullable=True)
    created_by = db.Column(db.Integer, db.<PERSON>ey("user.id"), nullable=False)
    user_id = db.Column(db.Integer, db.<PERSON>ey("user.id"), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = db.Column(db.DateTime, nullable=True)

    # Relationships
    tasks = db.relationship('Task', backref='project', lazy=True, foreign_keys='Task.project_id')
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_projects')
    owner = db.relationship('User', foreign_keys=[user_id], backref='owned_projects')

    def __repr__(self):
        return f"<Project {self.name}>"

    def to_dict(self):
        """Convert project object to dictionary"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "status": self.status,
            "priority": self.priority,
            "start_date": self.start_date.isoformat() if self.start_date else None,
            "end_date": self.end_date.isoformat() if self.end_date else None,
            "created_by": self.created_by,
            "user_id": self.user_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    def __repr__(self):
        return f"<Project {self.name}>"
