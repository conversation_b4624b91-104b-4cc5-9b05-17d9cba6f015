"""
Task-related models for Task Management System
"""

from app.helpers.extensions import db
from datetime import datetime


class Task(db.Model):
    """
    Task model for task management
    """
    __tablename__ = 'task'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text, nullable=True)
    status = db.Column(db.String(50), default="To Do")
    est_time = db.Column(db.Float)
    due_date = db.Column(db.DateTime)
    priority = db.Column(db.String(20))  # low, medium, high
    assignee_id = db.Column(db.<PERSON>te<PERSON>, db.<PERSON><PERSON>("user.id"))
    project_id = db.Column(db.Integer, db.<PERSON>Key("project.id"))
    created_by = db.Column(db.Integer, db.<PERSON><PERSON>("user.id"))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = db.Column(db.DateTime, nullable=True)

    # Relationships
    assignee = db.relationship("User", foreign_keys=[assignee_id], backref="assigned_tasks")
    creator = db.relationship("User", foreign_keys=[created_by], backref="created_tasks")
    project = db.relationship("Project", backref="tasks")

    def to_dict(self):
        """Convert task object to dictionary"""
        return {
            "id": self.id,
            "title": self.title,
            "description": self.description,
            "status": self.status,
            "est_time": self.est_time,
            "due_date": self.due_date.isoformat() if self.due_date else None,
            "priority": self.priority,
            "assignee_id": self.assignee_id,
            "project_id": self.project_id,
            "created_by": self.created_by,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "deleted_at": self.deleted_at.isoformat() if self.deleted_at else None
        }

    def __repr__(self):
        return f"<Task {self.title}>"


class Attachment(db.Model):
    """
    File attachments for tasks
    """
    __tablename__ = 'attachment'
    
    id = db.Column(db.Integer, primary_key=True)
    task_id = db.Column(db.Integer, db.ForeignKey("task.id"), nullable=False)
    file_url = db.Column(db.String(255), nullable=False)
    file_name = db.Column(db.String(255), nullable=True)
    file_size = db.Column(db.Integer, nullable=True)
    file_type = db.Column(db.String(50), nullable=True)
    uploaded_by = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)
    deleted_at = db.Column(db.DateTime, nullable=True)

    # Relationships
    task = db.relationship("Task", backref=db.backref("attachments", lazy=True))
    uploader = db.relationship("User", backref=db.backref("uploaded_attachments", lazy=True))

    def to_dict(self):
        """Convert attachment object to dictionary"""
        return {
            "id": self.id,
            "task_id": self.task_id,
            "file_url": self.file_url,
            "file_name": self.file_name,
            "file_size": self.file_size,
            "file_type": self.file_type,
            "uploaded_by": self.uploaded_by,
            "uploaded_at": self.uploaded_at.isoformat() if self.uploaded_at else None,
        }

    def __repr__(self):
        return f"<Attachment {self.file_name}>"


class Comment(db.Model):
    """
    Comments for tasks
    """
    __tablename__ = 'comment'

    id = db.Column(db.Integer, primary_key=True)
    task_id = db.Column(db.Integer, db.ForeignKey("task.id"), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    content = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = db.Column(db.DateTime, nullable=True)

    # Relationships
    task = db.relationship("Task", backref=db.backref("comments", lazy=True))
    user = db.relationship("User", backref=db.backref("comments", lazy=True))

    def to_dict(self):
        """Convert comment object to dictionary"""
        return {
            "id": self.id,
            "task_id": self.task_id,
            "user_id": self.user_id,
            "content": self.content,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    def __repr__(self):
        return f"<Comment {self.id} for Task {self.task_id}>"
