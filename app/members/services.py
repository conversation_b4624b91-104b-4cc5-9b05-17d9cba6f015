from app.models import Project, User, UserProject, Role
from app.models.api_response import ApiResponse
from app.helpers.extensions import db
from app.projects.permissions import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Project<PERSON>ole, ProjectPermission
from datetime import datetime
from typing import Tuple, Dict, Any


def get_project_members(project_id: int, user_id: int) -> <PERSON><PERSON>[Dict[str, Any], int]:
    """Get all members of a project"""
    try:
        # Check if user can view project
        if not ProjectPermissionChecker.can_access_project(user_id, project_id):
            response = ApiResponse.failure("Access denied", code=403)
            return response.to_dict(), response.code
        
        project = Project.query.get(project_id)
        if not project or project.deleted_at:
            response = ApiResponse.failure("Project not found", code=404)
            return response.to_dict(), response.code
        
        # Get all project members
        members_query = db.session.query(
            UserProject, User, Role
        ).join(
            User, UserProject.user_id == User.id
        ).join(
            Role, UserProject.role_id == Role.id
        ).filter(
            UserProject.project_id == project_id
        ).all()
        
        members = []
        for user_project, user, role in members_query:
            members.append({
                "user_id": user.id,
                "username": user.username,
                "email": user.email,
                "role": role.name,
                "role_description": role.description,
                "joined_at": user_project.created_at.isoformat() if user_project.created_at else None
            })
        
        # Add project creator if not already in members
        creator = User.query.get(project.created_by)
        creator_in_members = any(m["user_id"] == project.created_by for m in members)
        
        if creator and not creator_in_members:
            members.insert(0, {
                "user_id": creator.id,
                "username": creator.username,
                "email": creator.email,
                "role": ProjectRole.OWNER.value,
                "role_description": "Project creator and owner",
                "joined_at": project.created_at.isoformat() if project.created_at else None
            })
        
        response = ApiResponse.success(
            "Project members retrieved successfully",
            data={
                "project_id": project_id,
                "project_name": project.name,
                "members": members,
                "total_members": len(members)
            }
        )
        return response.to_dict(), response.code
        
    except Exception as e:
        response = ApiResponse.failure(f"Error retrieving project members: {str(e)}", code=500)
        return response.to_dict(), response.code

def add_project_member(project_id: int, user_id: int, data: Dict[str, Any]) -> Tuple[Dict[str, Any], int]:
    """Add a member to project"""
    try:
        # Check if user can manage members
        if not ProjectPermissionChecker.has_project_permission(user_id, project_id, ProjectPermission.MANAGE_MEMBERS):
            response = ApiResponse.failure("Permission denied. You cannot manage project members.", code=403)
            return response.to_dict(), response.code

        project = Project.query.get(project_id)
        if not project or project.deleted_at:
            return {"message": "Project not found"}, 404

        member_user_id = data.get("user_id")
        role_name = data.get("role", "project_member")

        if not member_user_id:
            return {"message": "user_id is required"}, 400

        # Check if user exists
        member_user = User.query.get(member_user_id)
        if not member_user:
            return {"message": "User not found"}, 404

        # Check if user is already a member
        existing_member = UserProject.query.filter_by(
            user_id=member_user_id,
            project_id=project_id
        ).first()

        if existing_member:
            return {"message": "User is already a member of this project"}, 400

        # Check if user is project creator
        if member_user_id == project.created_by:
            return {"message": "Project creator is automatically an owner"}, 400

        # Validate role
        role = Role.query.filter_by(name=role_name).first()
        if not role:
            return {"message": f"Invalid role: {role_name}"}, 400

        # Create user project relationship
        user_project = UserProject(
            user_id=member_user_id,
            project_id=project_id,
            role_id=role.id
        )

        db.session.add(user_project)
        db.session.commit()

        return {
            "message": "Member added successfully",
            "member": {
                "user_id": member_user.id,
                "username": member_user.username,
                "email": member_user.email,
                "role": role.name,
                "joined_at": user_project.created_at.isoformat()
            }
        }, 201

    except Exception as e:
        db.session.rollback()
        return {"message": f"Error adding project member: {str(e)}"}, 500

def update_member_role(project_id: int, user_id: int, member_user_id: int, data: Dict[str, Any]) -> Tuple[Dict[str, Any], int]:
    """Update a member's role in project"""
    try:
        # Check if user can manage members
        if not ProjectPermissionChecker.has_project_permission(user_id, project_id, ProjectPermission.MANAGE_MEMBERS):
            return {"message": "Permission denied. You cannot manage project members."}, 403

        project = Project.query.get(project_id)
        if not project or project.deleted_at:
            return {"message": "Project not found"}, 404

        new_role_name = data.get("role")
        if not new_role_name:
            return {"message": "role is required"}, 400

        # Check if member exists
        user_project = UserProject.query.filter_by(
            user_id=member_user_id,
            project_id=project_id
        ).first()

        if not user_project:
            return {"message": "User is not a member of this project"}, 404

        # Cannot change role of project creator
        if member_user_id == project.created_by:
            return {"message": "Cannot change role of project creator"}, 400

        # Validate new role
        new_role = Role.query.filter_by(name=new_role_name).first()
        if not new_role:
            return {"message": f"Invalid role: {new_role_name}"}, 400

        # Update role
        old_role = Role.query.get(user_project.role_id)
        user_project.role_id = new_role.id
        user_project.updated_at = datetime.utcnow()

        db.session.commit()

        member_user = User.query.get(member_user_id)

        return {
            "message": "Member role updated successfully",
            "member": {
                "user_id": member_user.id,
                "username": member_user.username,
                "email": member_user.email,
                "old_role": old_role.name if old_role else None,
                "new_role": new_role.name,
                "updated_at": user_project.updated_at.isoformat()
            }
        }, 200

    except Exception as e:
        db.session.rollback()
        return {"message": f"Error updating member role: {str(e)}"}, 500

def remove_project_member(project_id: int, user_id: int, member_user_id: int) -> Tuple[Dict[str, Any], int]:
    """Remove a member from project"""
    try:
        # Check if user can manage members
        if not ProjectPermissionChecker.has_project_permission(user_id, project_id, ProjectPermission.MANAGE_MEMBERS):
            return {"message": "Permission denied. You cannot manage project members."}, 403

        project = Project.query.get(project_id)
        if not project or project.deleted_at:
            return {"message": "Project not found"}, 404

        # Check if member exists
        user_project = UserProject.query.filter_by(
            user_id=member_user_id,
            project_id=project_id
        ).first()

        if not user_project:
            return {"message": "User is not a member of this project"}, 404

        # Cannot remove project creator
        if member_user_id == project.created_by:
            return {"message": "Cannot remove project creator"}, 400

        # Remove member
        member_user = User.query.get(member_user_id)
        db.session.delete(user_project)
        db.session.commit()

        return {
            "message": "Member removed successfully",
            "removed_member": {
                "user_id": member_user.id if member_user else member_user_id,
                "username": member_user.username if member_user else "Unknown"
            }
        }, 200

    except Exception as e:
        db.session.rollback()
        return {"message": f"Error removing project member: {str(e)}"}, 500

def get_user_projects(user_id: int) -> Tuple[Dict[str, Any], int]:
    """Get all projects where user is a member"""
    try:
        # Get projects where user is creator
        created_projects = Project.query.filter_by(
            created_by=user_id,
            deleted_at=None
        ).all()

        # Get projects where user is a member
        member_projects_query = db.session.query(
            Project, UserProject, Role
        ).join(
            UserProject, Project.id == UserProject.project_id
        ).join(
            Role, UserProject.role_id == Role.id
        ).filter(
            UserProject.user_id == user_id,
            Project.deleted_at.is_(None)
        ).all()

        projects = []

        # Add created projects
        for project in created_projects:
            projects.append({
                "project_id": project.id,
                "name": project.name,
                "description": project.description,
                "status": project.status,
                "priority": project.priority,
                "role": ProjectRole.OWNER.value,
                "is_creator": True,
                "created_at": project.created_at.isoformat() if project.created_at else None
            })

        # Add member projects (avoid duplicates)
        created_project_ids = {p.id for p in created_projects}
        for project, user_project, role in member_projects_query:
            if project.id not in created_project_ids:
                projects.append({
                    "project_id": project.id,
                    "name": project.name,
                    "description": project.description,
                    "status": project.status,
                    "priority": project.priority,
                    "role": role.name,
                    "is_creator": False,
                    "joined_at": user_project.created_at.isoformat() if user_project.created_at else None
                })

        return {
            "projects": projects,
            "total_projects": len(projects)
        }, 200

    except Exception as e:
        return {"message": f"Error retrieving user projects: {str(e)}"}, 500

