swagger: "2.0"
info:
  title: Task Management System API
  version: 1.0.0
  description: |
    API for user registration, login, and task management.

    ## Authentication
    This API uses JWT Bearer tokens for authentication. To access protected endpoints:
    1. <PERSON><PERSON> using `/auth/login` to get an access token
    2. Include the token in the Authorization header: `Bearer {token}`

    ## Testing
    You can test the API endpoints directly from this documentation interface.

basePath: /
schemes:
  - http
produces:
  - application/json
consumes:
  - application/json

securityDefinitions:
  BearerAuth:
    type: apiKey
    name: Authorization
    in: header
    description: "JWT Authorization header using the Bearer scheme. Example: 'Bearer {token}'"

security:
  - BearerAuth: []

paths:
  # Health Check
  /:
    get:
      tags:
        - Health
      summary: API Health Check
      description: Check if the API is running
      security: []
      responses:
        200:
          description: API is running
          schema:
            type: object
            properties:
              message:
                type: string
                example: "API is running"

  /auth/login:
    post:
      tags:
        - Authentication
      summary: User login
      description: Authenticate user and return JWT token
      security: []
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/UserLogin'
      responses:
        200:
          description: Login successful, JWT token returned
          schema:
            $ref: '#/definitions/LoginResponse'
        400:
          description: Bad request - Missing required fields or invalid email format
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Invalid credentials
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/logout:
    post:
      tags:
        - Authentication
      summary: Logout user
      description: Revoke the current JWT token
      security:
        - BearerAuth: []
      responses:
        200:
          description: Logged out successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/register:
    post:
      tags:
        - Authentication
      summary: Register a new user
      description: Create a new user account
      security: []
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/UserRegister'
      responses:
        201:
          description: User created successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        400:
          description: Invalid input or user already exists
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/me:
    get:
      tags:
        - Authentication
      summary: Get current user info
      description: Retrieve information about the currently authenticated user
      security:
        - BearerAuth: []
      responses:
        200:
          description: User information retrieved successfully
          schema:
            $ref: '#/definitions/UserResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/github:
    get:
      tags:
        - Authentication
      summary: Initiate GitHub OAuth login
      description: |
        Redirect user to GitHub for OAuth authentication.

        **Note**: This endpoint returns a 302 redirect. In Swagger UI, you'll see an error because it cannot follow redirects automatically.

        **To test properly**:
        1. Copy the URL: `http://tms.uit.local:8084/auth/github`
        2. Open it in a new browser tab
        3. Complete the GitHub OAuth flow

        **Alternative**: Use the "Try it out" button below, then copy the redirect URL from the response headers.
      security: []
      responses:
        302:
          description: Redirect to GitHub OAuth authorization page
          headers:
            Location:
              type: string
              description: GitHub OAuth authorization URL
              example: "https://github.com/login/oauth/authorize?client_id=..."
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/github/callback:
    get:
      tags:
        - Authentication
      summary: GitHub OAuth callback
      description: Handle GitHub OAuth callback and authenticate user
      security: []
      parameters:
        - name: code
          in: query
          description: Authorization code from GitHub
          required: true
          type: string
        - name: state
          in: query
          description: State parameter for CSRF protection
          required: false
          type: string
        - name: error
          in: query
          description: Error parameter if OAuth failed
          required: false
          type: string
      responses:
        200:
          description: GitHub authentication successful
          schema:
            $ref: '#/definitions/GitHubAuthResponse'
        400:
          description: Bad request - Invalid parameters or OAuth error
          schema:
            $ref: '#/definitions/ErrorResponse'
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/github/link:
    post:
      tags:
        - Authentication
      summary: Link GitHub account to current user
      description: Link a GitHub account to the currently authenticated user
      security:
        - BearerAuth: []
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/GitHubLinkRequest'
      responses:
        200:
          description: GitHub account linked successfully
          schema:
            $ref: '#/definitions/UserResponse'
        400:
          description: Bad request - Invalid token or account already linked
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/github/unlink:
    post:
      tags:
        - Authentication
      summary: Unlink GitHub account from current user
      description: Remove GitHub account link from the currently authenticated user
      security:
        - BearerAuth: []
      responses:
        200:
          description: GitHub account unlinked successfully
          schema:
            $ref: '#/definitions/UserResponse'
        400:
          description: Bad request - No GitHub account linked or cannot unlink
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/ErrorResponse'

  /auth/github/url:
    get:
      tags:
        - Authentication
      summary: Get GitHub OAuth authorization URL
      description: |
        Get the GitHub OAuth authorization URL without redirecting.
        This is useful for testing in Swagger UI or getting the URL programmatically.

        **Usage**:
        1. Call this endpoint to get the authorization URL
        2. Copy the URL and open it in a browser
        3. Complete GitHub OAuth flow
        4. Use the JWT token returned from the callback
      security: []
      responses:
        200:
          description: GitHub OAuth authorization URL
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: true
              message:
                type: string
                example: "GitHub authorization URL generated"
              data:
                type: object
                properties:
                  authorization_url:
                    type: string
                    example: "https://github.com/login/oauth/authorize?client_id=..."
                  instructions:
                    type: array
                    items:
                      type: string
                    example:
                      - "Copy the authorization_url above"
                      - "Open it in a new browser tab"
                      - "Authorize the application on GitHub"
                      - "You will be redirected back with a JWT token"
              code:
                type: integer
                example: 200
        500:
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'

  # Task management endpoints
  /tasks/list_tasks:
    get:
      tags:
        - Tasks
      summary: Get list of tasks
      description: Retrieve all tasks, optionally filtered by assignee. Returns unassigned tasks by default.
      security:
        - BearerAuth: []
      parameters:
        - name: assignee_id
          in: query
          description: Filter tasks by assignee ID. If not provided, returns unassigned tasks.
          required: false
          type: integer
          example: 1
      responses:
        200:
          description: List of tasks retrieved successfully
          schema:
            $ref: '#/definitions/TaskListResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'

  /tasks/create_task_view:
    post:
      tags:
        - Tasks
      summary: Create a new task
      description: Create a new task with the provided details
      security:
        - BearerAuth: []
      parameters:
        - in: body
          name: body
          required: true
          schema:
            $ref: '#/definitions/TaskCreate'
      responses:
        201:
          description: Task created successfully
          schema:
            $ref: '#/definitions/TaskCreateResponse'
        400:
          description: Bad request - Invalid input data
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'

  /tasks/update_task_view/{task_id}:
    put:
      tags:
        - Tasks
      summary: Update an existing task
      description: Update task details. Only the task creator can update the task.
      security:
        - BearerAuth: []
      parameters:
        - name: task_id
          in: path
          description: ID of the task to update
          required: true
          type: integer
          example: 1
        - in: body
          name: body
          required: true
          schema:
            $ref: '#/definitions/TaskUpdate'
      responses:
        200:
          description: Task updated successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        400:
          description: Bad request - Invalid input data
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        403:
          description: Forbidden - Permission denied (only creator can update)
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: Task not found
          schema:
            $ref: '#/definitions/ErrorResponse'

  /tasks/delete_task_view/{task_id}:
    delete:
      tags:
        - Tasks
      summary: Delete a task
      description: Soft delete a task. Only the task creator can delete the task.
      security:
        - BearerAuth: []
      parameters:
        - name: task_id
          in: path
          description: ID of the task to delete
          required: true
          type: integer
          example: 1
      responses:
        200:
          description: Task deleted successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        401:
          description: Unauthorized - Invalid or missing JWT token
          schema:
            $ref: '#/definitions/ErrorResponse'
        403:
          description: Forbidden - Permission denied (only creator can delete)
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: Task not found
          schema:
            $ref: '#/definitions/ErrorResponse'

  # Projects API
  /projects/:
    get:
      tags:
        - Projects
      summary: Get all projects for user
      description: Retrieve all projects that the user has access to (created or member of)
      security:
        - BearerAuth: []
      parameters:
        - name: include_deleted
          in: query
          description: Include soft-deleted projects
          required: false
          type: boolean
          default: false
      responses:
        200:
          description: Projects retrieved successfully
          schema:
            $ref: '#/definitions/ProjectListResponse'
        500:
          description: Server error
          schema:
            $ref: '#/definitions/ErrorResponse'

    post:
      tags:
        - Projects
      summary: Create a new project
      description: Create a new project with the provided details
      security:
        - BearerAuth: []
      parameters:
        - in: body
          name: body
          required: true
          schema:
            $ref: '#/definitions/ProjectCreate'
      responses:
        201:
          description: Project created successfully
          schema:
            $ref: '#/definitions/ProjectResponse'
        400:
          description: Bad request - Invalid input data
          schema:
            $ref: '#/definitions/ErrorResponse'
        401:
          description: Unauthorized
          schema:
            $ref: '#/definitions/ErrorResponse'

  /projects/{project_id}:
    get:
      tags:
        - Projects
      summary: Get project by ID
      description: Retrieve a specific project by its ID
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: integer
      responses:
        200:
          description: Project retrieved successfully
          schema:
            $ref: '#/definitions/ProjectResponse'
        403:
          description: Access denied
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: Project not found
          schema:
            $ref: '#/definitions/ErrorResponse'

    put:
      tags:
        - Projects
      summary: Update project
      description: Update project details. Only project owner can update.
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: integer
        - in: body
          name: body
          required: true
          schema:
            $ref: '#/definitions/ProjectUpdate'
      responses:
        200:
          description: Project updated successfully
          schema:
            $ref: '#/definitions/ProjectResponse'
        400:
          description: Bad request
          schema:
            $ref: '#/definitions/ErrorResponse'
        403:
          description: Permission denied
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: Project not found
          schema:
            $ref: '#/definitions/ErrorResponse'

    delete:
      tags:
        - Projects
      summary: Delete project
      description: Soft delete a project. Use hard_delete=true for permanent deletion.
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: integer
        - name: hard_delete
          in: query
          description: Permanently delete the project
          required: false
          type: boolean
          default: false
      responses:
        200:
          description: Project deleted successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        403:
          description: Permission denied
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: Project not found
          schema:
            $ref: '#/definitions/ErrorResponse'

  /projects/{project_id}/restore:
    post:
      tags:
        - Projects
      summary: Restore deleted project
      description: Restore a soft-deleted project
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: integer
      responses:
        200:
          description: Project restored successfully
          schema:
            $ref: '#/definitions/ProjectResponse'
        403:
          description: Permission denied
          schema:
            $ref: '#/definitions/ErrorResponse'
        404:
          description: Project not found
          schema:
            $ref: '#/definitions/ErrorResponse'

  # Project Members API - See members.yaml for detailed documentation
  /members/projects/{project_id}:
    $ref: 'members.yaml#/paths/~1members~1projects~1{project_id}'

  /members/projects/{project_id}/users/{user_id}:
    $ref: 'members.yaml#/paths/~1members~1projects~1{project_id}~1users~1{user_id}'

  /members/my-projects:
    $ref: 'members.yaml#/paths/~1members~1my-projects'

  # Statistics API - See statistics.yaml for detailed documentation
  /statistics/dashboard:
    $ref: 'statistics.yaml#/paths/~1statistics~1dashboard'

  /statistics/user:
    $ref: 'statistics.yaml#/paths/~1statistics~1user'

  /statistics/user/{user_id}:
    $ref: 'statistics.yaml#/paths/~1statistics~1user~1{user_id}'

  /statistics/project/{project_id}:
    $ref: 'statistics.yaml#/paths/~1statistics~1project~1{project_id}'

# All definitions in one place
definitions:
  # Common Response Schemas
  SuccessResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Operation completed successfully"
      data:
        type: object
        example: null
      code:
        type: integer
        example: 200

  ErrorResponse:
    type: object
    properties:
      success:
        type: boolean
        example: false
      message:
        type: string
        example: "Operation failed"
      data:
        type: object
        example: null
      code:
        type: integer
        example: 400

  # User-specific Responses
  UserResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "User retrieved successfully"
      data:
        $ref: '#/definitions/User'
      code:
        type: integer
        example: 200

  UserListResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Users retrieved successfully"
      data:
        type: array
        items:
          $ref: '#/definitions/User'
      code:
        type: integer
        example: 200

  LoginResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Login successful"
      data:
        type: object
        properties:
          access_token:
            type: string
            example: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
            description: "JWT access token"
      code:
        type: integer
        example: 200

  # Task-specific Responses
  TaskResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Task retrieved successfully"
      data:
        $ref: '#/definitions/Task'
      code:
        type: integer
        example: 200

  TaskListResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Tasks retrieved successfully"
      data:
        type: array
        items:
          $ref: '#/definitions/Task'
      code:
        type: integer
        example: 200

  TaskCreateResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "Task created successfully"
      data:
        type: object
        properties:
          id:
            type: integer
            example: 1
            description: "ID of the created task"
      code:
        type: integer
        example: 201

  # User Schemas (from paths/auth.yaml)
  User:
    type: object
    properties:
      id:
        type: integer
        example: 1
      username:
        type: string
        example: "testuser"
      email:
        type: string
        format: email
        example: "<EMAIL>"
      phone:
        type: string
        example: "******-123-4567"
      bio:
        type: string
        example: "This is a user bio"
      created_at:
        type: string
        format: date-time
        example: "2024-01-01T10:00:00Z"
      updated_at:
        type: string
        format: date-time
        example: "2024-01-01T10:00:00Z"
      two_factor_enabled:
        type: boolean
        example: false
      github_id:
        type: string
        example: "12345678"
        description: "GitHub user ID (if linked)"
      github_username:
        type: string
        example: "octocat"
        description: "GitHub username (if linked)"
      github_avatar_url:
        type: string
        example: "https://avatars.githubusercontent.com/u/583231?v=4"
        description: "GitHub avatar URL (if linked)"
      auth_provider:
        type: string
        example: "local"
        description: "Authentication provider (local, github, etc.)"

  UserRegister:
    type: object
    required:
      - username
      - email
      - password
    properties:
      username:
        type: string
        example: "testuser"
        minLength: 3
        maxLength: 100
        description: "Username (3-100 characters)"
      email:
        type: string
        format: email
        example: "<EMAIL>"
        description: "Valid email address"
      password:
        type: string
        example: "Admin_12345"
        minLength: 8
        description: "Password (minimum 8 characters)"

  UserLogin:
    type: object
    required:
      - email
      - password
    properties:
      email:
        type: string
        format: email
        example: "<EMAIL>"
        description: "User email address"
      password:
        type: string
        example: "Admin_12345"
        description: "User password"

  # GitHub Authentication Schemas
  GitHubAuthResponse:
    type: object
    properties:
      success:
        type: boolean
        example: true
      message:
        type: string
        example: "GitHub authentication successful"
      data:
        type: object
        properties:
          access_token:
            type: string
            example: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
            description: "JWT access token"
          user:
            $ref: '#/definitions/User'
      code:
        type: integer
        example: 200

  GitHubLinkRequest:
    type: object
    required:
      - access_token
    properties:
      access_token:
        type: string
        example: "gho_xxxxxxxxxxxxxxxxxxxx"
        description: "GitHub access token obtained from OAuth flow"

  # Task Schemas
  Task:
    type: object
    properties:
      id:
        type: integer
        example: 1
      title:
        type: string
        example: "Complete project documentation"
      description:
        type: string
        x-nullable: true
        example: "Write comprehensive documentation for the project"
      status:
        type: string
        example: "To Do"
        enum: ["To Do", "In Progress", "Done"]
      est_time:
        type: number
        format: float
        x-nullable: true
        example: 4.5
        description: "Estimated time in hours"
      due_date:
        type: string
        format: date-time
        x-nullable: true
        example: "2024-12-31T23:59:59Z"
      priority:
        type: string
        example: "Medium"
        enum: ["Low", "Medium", "High"]
      assignee_id:
        type: integer
        x-nullable: true
        example: 1
      project_id:
        type: integer
        x-nullable: true
        example: 1
      created_by:
        type: integer
        example: 1
      created_at:
        type: string
        format: date-time
        example: "2024-01-01T10:00:00Z"
      updated_at:
        type: string
        format: date-time
        example: "2024-01-01T10:00:00Z"
      deleted_at:
        type: string
        format: date-time
        x-nullable: true

  TaskCreate:
    type: object
    required:
      - title
    properties:
      title:
        type: string
        example: "Complete project documentation"
        description: "Task title (required)"
        minLength: 1
        maxLength: 255
      description:
        type: string
        x-nullable: true
        example: "Write comprehensive documentation for the project"
      status:
        type: string
        example: "To Do"
        default: "To Do"
        enum: ["To Do", "In Progress", "Done"]
      est_time:
        type: number
        format: float
        x-nullable: true
        example: 4.5
        description: "Estimated time in hours"
        minimum: 0
      due_date:
        type: string
        format: date-time
        x-nullable: true
        example: "2024-12-31T23:59:59Z"
        description: "Due date in ISO 8601 format"
      priority:
        type: string
        example: "Medium"
        default: "Medium"
        enum: ["Low", "Medium", "High"]
      assignee_id:
        type: integer
        x-nullable: true
        example: 1
        description: "ID of the user assigned to this task"
      project_id:
        type: integer
        x-nullable: true
        example: 1
        description: "ID of the project this task belongs to"

  TaskUpdate:
    type: object
    properties:
      title:
        type: string
        example: "Updated task title"
        minLength: 1
        maxLength: 255
      description:
        type: string
        x-nullable: true
        example: "Updated task description"
      status:
        type: string
        example: "In Progress"
        enum: ["To Do", "In Progress", "Done"]
      est_time:
        type: number
        format: float
        x-nullable: true
        example: 6.0
        description: "Estimated time in hours"
        minimum: 0
      due_date:
        type: string
        format: date-time
        x-nullable: true
        example: "2024-12-31T23:59:59Z"
        description: "Due date in ISO 8601 format"
      priority:
        type: string
        example: "High"
        enum: ["Low", "Medium", "High"]
      assignee_id:
        type: integer
        x-nullable: true
        example: 2
        description: "ID of the user assigned to this task"
      project_id:
        type: integer
        x-nullable: true
        example: 1
        description: "ID of the project this task belongs to"

  # Project Definitions - From projects.yaml
  Project:
    $ref: 'projects.yaml#/definitions/Project'

  ProjectCreate:
    $ref: 'projects.yaml#/definitions/ProjectCreate'

  ProjectUpdate:
    $ref: 'projects.yaml#/definitions/ProjectUpdate'

  ProjectResponse:
    $ref: 'projects.yaml#/definitions/ProjectResponse'

  ProjectListResponse:
    $ref: 'projects.yaml#/definitions/ProjectListResponse'

  # Member Definitions - From members.yaml
  ProjectMember:
    $ref: 'members.yaml#/definitions/ProjectMember'

  UserProject:
    $ref: 'members.yaml#/definitions/UserProject'

  AddMemberRequest:
    $ref: 'members.yaml#/definitions/AddMemberRequest'

  UpdateMemberRoleRequest:
    $ref: 'members.yaml#/definitions/UpdateMemberRoleRequest'

  ProjectMembersResponse:
    $ref: 'members.yaml#/definitions/ProjectMembersResponse'

  MemberResponse:
    $ref: 'members.yaml#/definitions/MemberResponse'

  MemberUpdateResponse:
    $ref: 'members.yaml#/definitions/MemberUpdateResponse'

  MemberRemoveResponse:
    $ref: 'members.yaml#/definitions/MemberRemoveResponse'

  UserProjectsResponse:
    $ref: 'members.yaml#/definitions/UserProjectsResponse'

  # Statistics Definitions - From statistics.yaml
  DashboardStats:
    $ref: 'statistics.yaml#/definitions/DashboardStats'

  UserStats:
    $ref: 'statistics.yaml#/definitions/UserStats'

  ProjectStats:
    $ref: 'statistics.yaml#/definitions/ProjectStats'

  DashboardStatsResponse:
    $ref: 'statistics.yaml#/definitions/DashboardStatsResponse'

  UserStatsResponse:
    $ref: 'statistics.yaml#/definitions/UserStatsResponse'

  ProjectStatsResponse:
    $ref: 'statistics.yaml#/definitions/ProjectStatsResponse'