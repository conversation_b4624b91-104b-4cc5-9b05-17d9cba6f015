from app.models import Project, Task, User, UserProject
from app.helpers.extensions import db
from app.projects.permissions import <PERSON><PERSON>er<PERSON><PERSON><PERSON><PERSON>
from datetime import datetime, timedelta
from typing import Dict, <PERSON>, <PERSON><PERSON>, List
from sqlalchemy import func, and_, or_


def get_project_statistics(project_id: int, user_id: int) -> Tuple[Dict[str, Any], int]:
    """Get comprehensive statistics for a specific project"""
    try:
        # Check if user can view project
        if not ProjectPermissionChecker.can_access_project(user_id, project_id):
            return {"message": "Access denied"}, 403
        
        project = Project.query.get(project_id)
        if not project or project.deleted_at:
            return {"message": "Project not found"}, 404
        
        # Task statistics
        total_tasks = Task.query.filter_by(project_id=project_id, deleted_at=None).count()
        completed_tasks = Task.query.filter_by(project_id=project_id, status="Done", deleted_at=None).count()
        in_progress_tasks = Task.query.filter_by(project_id=project_id, status="In Progress", deleted_at=None).count()
        todo_tasks = Task.query.filter_by(project_id=project_id, status="To Do", deleted_at=None).count()
        
        # Task priority breakdown
        high_priority = Task.query.filter_by(project_id=project_id, priority="high", deleted_at=None).count()
        medium_priority = Task.query.filter_by(project_id=project_id, priority="medium", deleted_at=None).count()
        low_priority = Task.query.filter_by(project_id=project_id, priority="low", deleted_at=None).count()
        
        # Overdue tasks
        overdue_tasks = Task.query.filter(
            Task.project_id == project_id,
            Task.due_date < datetime.utcnow(),
            Task.status != "Done",
            Task.deleted_at.is_(None)
        ).count()
        
        # Member statistics
        total_members = UserProject.query.filter_by(project_id=project_id).count()
        # Add creator if not in UserProject
        if not UserProject.query.filter_by(project_id=project_id, user_id=project.created_by).first():
            total_members += 1
        
        # Task assignment statistics
        assigned_tasks = Task.query.filter(
            Task.project_id == project_id,
            Task.assignee_id.isnot(None),
            Task.deleted_at.is_(None)
        ).count()
        unassigned_tasks = total_tasks - assigned_tasks
        
        # Time estimation statistics
        total_estimated_time = db.session.query(func.sum(Task.est_time)).filter(
            Task.project_id == project_id,
            Task.deleted_at.is_(None)
        ).scalar() or 0
        
        completed_estimated_time = db.session.query(func.sum(Task.est_time)).filter(
            Task.project_id == project_id,
            Task.status == "Done",
            Task.deleted_at.is_(None)
        ).scalar() or 0
        
        # Progress calculation
        progress_percentage = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
        
        # Recent activity (tasks created in last 7 days)
        week_ago = datetime.utcnow() - timedelta(days=7)
        recent_tasks = Task.query.filter(
            Task.project_id == project_id,
            Task.created_at >= week_ago,
            Task.deleted_at.is_(None)
        ).count()
        
        return {
            "project_id": project_id,
            "project_name": project.name,
            "project_status": project.status,
            "project_priority": project.priority,
            "statistics": {
                "tasks": {
                    "total": total_tasks,
                    "completed": completed_tasks,
                    "in_progress": in_progress_tasks,
                    "todo": todo_tasks,
                    "overdue": overdue_tasks,
                    "assigned": assigned_tasks,
                    "unassigned": unassigned_tasks,
                    "recent_week": recent_tasks
                },
                "priority_breakdown": {
                    "high": high_priority,
                    "medium": medium_priority,
                    "low": low_priority
                },
                "time_estimation": {
                    "total_estimated_hours": float(total_estimated_time),
                    "completed_estimated_hours": float(completed_estimated_time),
                    "remaining_estimated_hours": float(total_estimated_time - completed_estimated_time)
                },
                "progress": {
                    "percentage": round(progress_percentage, 2),
                    "completed_tasks": completed_tasks,
                    "total_tasks": total_tasks
                },
                "team": {
                    "total_members": total_members
                }
            }
        }, 200
        
    except Exception as e:
        return {"message": f"Error retrieving project statistics: {str(e)}"}, 500


def get_user_statistics(user_id: int) -> Tuple[Dict[str, Any], int]:
    """Get comprehensive statistics for a user"""
    try:
        user = User.query.get(user_id)
        if not user:
            return {"message": "User not found"}, 404
        
        # Projects statistics
        created_projects = Project.query.filter_by(created_by=user_id, deleted_at=None).count()
        member_projects = UserProject.query.filter_by(user_id=user_id).count()
        total_projects = created_projects + member_projects
        
        # Remove duplicates (if user is both creator and member)
        duplicate_projects = db.session.query(UserProject).join(Project).filter(
            UserProject.user_id == user_id,
            Project.created_by == user_id,
            Project.deleted_at.is_(None)
        ).count()
        total_projects -= duplicate_projects
        
        # Tasks statistics
        created_tasks = Task.query.filter_by(created_by=user_id, deleted_at=None).count()
        assigned_tasks = Task.query.filter_by(assignee_id=user_id, deleted_at=None).count()
        completed_assigned_tasks = Task.query.filter_by(
            assignee_id=user_id, 
            status="Done", 
            deleted_at=None
        ).count()
        
        # Overdue assigned tasks
        overdue_assigned_tasks = Task.query.filter(
            Task.assignee_id == user_id,
            Task.due_date < datetime.utcnow(),
            Task.status != "Done",
            Task.deleted_at.is_(None)
        ).count()
        
        # Recent activity
        week_ago = datetime.utcnow() - timedelta(days=7)
        recent_created_tasks = Task.query.filter(
            Task.created_by == user_id,
            Task.created_at >= week_ago,
            Task.deleted_at.is_(None)
        ).count()
        
        recent_completed_tasks = Task.query.filter(
            Task.assignee_id == user_id,
            Task.status == "Done",
            Task.updated_at >= week_ago,
            Task.deleted_at.is_(None)
        ).count()
        
        # Task completion rate
        completion_rate = (completed_assigned_tasks / assigned_tasks * 100) if assigned_tasks > 0 else 0
        
        return {
            "user_id": user_id,
            "username": user.username,
            "email": user.email,
            "statistics": {
                "projects": {
                    "total": total_projects,
                    "created": created_projects,
                    "member_of": member_projects
                },
                "tasks": {
                    "created": created_tasks,
                    "assigned": assigned_tasks,
                    "completed": completed_assigned_tasks,
                    "overdue": overdue_assigned_tasks,
                    "completion_rate": round(completion_rate, 2)
                },
                "recent_activity": {
                    "tasks_created_week": recent_created_tasks,
                    "tasks_completed_week": recent_completed_tasks
                }
            }
        }, 200
        
    except Exception as e:
        return {"message": f"Error retrieving user statistics: {str(e)}"}, 500


def get_dashboard_statistics(user_id: int) -> Tuple[Dict[str, Any], int]:
    """Get dashboard statistics for user"""
    try:
        user = User.query.get(user_id)
        if not user:
            return {"message": "User not found"}, 404
        
        # Get user's accessible projects
        created_project_ids = [p.id for p in Project.query.filter_by(created_by=user_id, deleted_at=None).all()]
        member_project_ids = [up.project_id for up in UserProject.query.filter_by(user_id=user_id).all()]
        all_project_ids = list(set(created_project_ids + member_project_ids))
        
        # Overall statistics
        total_projects = len(all_project_ids)
        
        # Tasks in user's projects
        total_tasks_in_projects = Task.query.filter(
            Task.project_id.in_(all_project_ids),
            Task.deleted_at.is_(None)
        ).count() if all_project_ids else 0
        
        # User's assigned tasks
        my_assigned_tasks = Task.query.filter_by(assignee_id=user_id, deleted_at=None).count()
        my_completed_tasks = Task.query.filter_by(assignee_id=user_id, status="Done", deleted_at=None).count()
        my_pending_tasks = my_assigned_tasks - my_completed_tasks
        
        # Overdue tasks assigned to user
        my_overdue_tasks = Task.query.filter(
            Task.assignee_id == user_id,
            Task.due_date < datetime.utcnow(),
            Task.status != "Done",
            Task.deleted_at.is_(None)
        ).count()
        
        # Recent activity (last 7 days)
        week_ago = datetime.utcnow() - timedelta(days=7)
        recent_projects_created = Project.query.filter(
            Project.created_by == user_id,
            Project.created_at >= week_ago,
            Project.deleted_at.is_(None)
        ).count()
        
        recent_tasks_created = Task.query.filter(
            Task.created_by == user_id,
            Task.created_at >= week_ago,
            Task.deleted_at.is_(None)
        ).count()
        
        recent_tasks_completed = Task.query.filter(
            Task.assignee_id == user_id,
            Task.status == "Done",
            Task.updated_at >= week_ago,
            Task.deleted_at.is_(None)
        ).count()
        
        # Project status breakdown
        active_projects = Project.query.filter(
            Project.id.in_(all_project_ids),
            Project.status == "active",
            Project.deleted_at.is_(None)
        ).count() if all_project_ids else 0
        
        completed_projects = Project.query.filter(
            Project.id.in_(all_project_ids),
            Project.status == "completed",
            Project.deleted_at.is_(None)
        ).count() if all_project_ids else 0
        
        return {
            "user_id": user_id,
            "username": user.username,
            "dashboard": {
                "overview": {
                    "total_projects": total_projects,
                    "active_projects": active_projects,
                    "completed_projects": completed_projects,
                    "total_tasks_in_projects": total_tasks_in_projects
                },
                "my_tasks": {
                    "assigned": my_assigned_tasks,
                    "completed": my_completed_tasks,
                    "pending": my_pending_tasks,
                    "overdue": my_overdue_tasks
                },
                "recent_activity": {
                    "projects_created_week": recent_projects_created,
                    "tasks_created_week": recent_tasks_created,
                    "tasks_completed_week": recent_tasks_completed
                }
            }
        }, 200
        
    except Exception as e:
        return {"message": f"Error retrieving dashboard statistics: {str(e)}"}, 500
