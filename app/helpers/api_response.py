"""
API Response Helper
Standardizes API response format across the application
"""

from typing import Any, Optional, Dict
from flask import jsonify


class ApiResponse:
    """
    Standardized API response format
    """
    
    @staticmethod
    def success(message: str, data: Any = None, code: int = 200) -> tuple:
        """
        Create a successful response
        
        Args:
            message: Success message
            data: Response data (optional)
            code: HTTP status code (default: 200)
            
        Returns:
            Tuple of (response_dict, status_code)
        """
        response = {
            "code": code,
            "data": data,
            "message": message,
            "success": True
        }
        return response, code
    
    @staticmethod
    def error(message: str, code: int = 400, data: Any = None) -> tuple:
        """
        Create an error response
        
        Args:
            message: Error message
            code: HTTP status code (default: 400)
            data: Additional error data (optional)
            
        Returns:
            Tuple of (response_dict, status_code)
        """
        response = {
            "code": code,
            "data": data,
            "message": message,
            "success": False
        }
        return response, code
    
    @staticmethod
    def not_found(message: str = "Resource not found", data: Any = None) -> tuple:
        """
        Create a 404 not found response
        
        Args:
            message: Error message
            data: Additional data (optional)
            
        Returns:
            Tuple of (response_dict, status_code)
        """
        return ApiResponse.error(message, 404, data)
    
    @staticmethod
    def forbidden(message: str = "Access denied", data: Any = None) -> tuple:
        """
        Create a 403 forbidden response
        
        Args:
            message: Error message
            data: Additional data (optional)
            
        Returns:
            Tuple of (response_dict, status_code)
        """
        return ApiResponse.error(message, 403, data)
    
    @staticmethod
    def unauthorized(message: str = "Unauthorized", data: Any = None) -> tuple:
        """
        Create a 401 unauthorized response
        
        Args:
            message: Error message
            data: Additional data (optional)
            
        Returns:
            Tuple of (response_dict, status_code)
        """
        return ApiResponse.error(message, 401, data)
    
    @staticmethod
    def bad_request(message: str = "Bad request", data: Any = None) -> tuple:
        """
        Create a 400 bad request response
        
        Args:
            message: Error message
            data: Additional data (optional)
            
        Returns:
            Tuple of (response_dict, status_code)
        """
        return ApiResponse.error(message, 400, data)
    
    @staticmethod
    def internal_error(message: str = "Internal server error", data: Any = None) -> tuple:
        """
        Create a 500 internal server error response
        
        Args:
            message: Error message
            data: Additional data (optional)
            
        Returns:
            Tuple of (response_dict, status_code)
        """
        return ApiResponse.error(message, 500, data)
    
    @staticmethod
    def created(message: str, data: Any = None) -> tuple:
        """
        Create a 201 created response
        
        Args:
            message: Success message
            data: Response data (optional)
            
        Returns:
            Tuple of (response_dict, status_code)
        """
        return ApiResponse.success(message, data, 201)
    
    @staticmethod
    def no_content(message: str = "Operation successful") -> tuple:
        """
        Create a 204 no content response
        
        Args:
            message: Success message
            
        Returns:
            Tuple of (response_dict, status_code)
        """
        return ApiResponse.success(message, None, 204)
