from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from .services import (
    get_project_statistics,
    get_user_statistics,
    get_dashboard_statistics
)

statistics_bp = Blueprint("statistics_bp", __name__, url_prefix="/statistics")


@statistics_bp.route("/dashboard", methods=["GET"])
@jwt_required()
def get_dashboard():
    """
    Get dashboard statistics for current user
    ---
    tags:
      - Statistics
    security:
      - BearerAuth: []
    produces:
      - application/json
    responses:
      200:
        description: Dashboard statistics
        schema:
          type: object
          properties:
            user_id:
              type: integer
            username:
              type: string
            dashboard:
              type: object
              properties:
                overview:
                  type: object
                  properties:
                    total_projects:
                      type: integer
                    active_projects:
                      type: integer
                    completed_projects:
                      type: integer
                    total_tasks_in_projects:
                      type: integer
                my_tasks:
                  type: object
                  properties:
                    assigned:
                      type: integer
                    completed:
                      type: integer
                    pending:
                      type: integer
                    overdue:
                      type: integer
                recent_activity:
                  type: object
                  properties:
                    projects_created_week:
                      type: integer
                    tasks_created_week:
                      type: integer
                    tasks_completed_week:
                      type: integer
      404:
        description: User not found
      500:
        description: Server error
    """
    current_user_id = int(get_jwt_identity())
    result, status = get_dashboard_statistics(current_user_id)
    return jsonify(result), status


@statistics_bp.route("/user", methods=["GET"])
@jwt_required()
def get_user_stats():
    """
    Get detailed statistics for current user
    ---
    tags:
      - Statistics
    security:
      - BearerAuth: []
    produces:
      - application/json
    responses:
      200:
        description: User statistics
        schema:
          type: object
          properties:
            user_id:
              type: integer
            username:
              type: string
            email:
              type: string
            statistics:
              type: object
              properties:
                projects:
                  type: object
                  properties:
                    total:
                      type: integer
                    created:
                      type: integer
                    member_of:
                      type: integer
                tasks:
                  type: object
                  properties:
                    created:
                      type: integer
                    assigned:
                      type: integer
                    completed:
                      type: integer
                    overdue:
                      type: integer
                    completion_rate:
                      type: number
                recent_activity:
                  type: object
                  properties:
                    tasks_created_week:
                      type: integer
                    tasks_completed_week:
                      type: integer
      404:
        description: User not found
      500:
        description: Server error
    """
    current_user_id = int(get_jwt_identity())
    result, status = get_user_statistics(current_user_id)
    return jsonify(result), status


@statistics_bp.route("/user/<int:user_id>", methods=["GET"])
@jwt_required()
def get_specific_user_stats(user_id):
    """
    Get statistics for a specific user (admin feature)
    ---
    tags:
      - Statistics
    security:
      - BearerAuth: []
    produces:
      - application/json
    parameters:
      - in: path
        name: user_id
        required: true
        type: integer
        description: ID of the user to get statistics for
    responses:
      200:
        description: User statistics
      404:
        description: User not found
      500:
        description: Server error
    """
    # Note: In a real application, you might want to add admin permission check here
    result, status = get_user_statistics(user_id)
    return jsonify(result), status


@statistics_bp.route("/project/<int:project_id>", methods=["GET"])
@jwt_required()
def get_project_stats(project_id):
    """
    Get comprehensive statistics for a specific project
    ---
    tags:
      - Statistics
    security:
      - BearerAuth: []
    produces:
      - application/json
    parameters:
      - in: path
        name: project_id
        required: true
        type: integer
        description: ID of the project to get statistics for
    responses:
      200:
        description: Project statistics
        schema:
          type: object
          properties:
            project_id:
              type: integer
            project_name:
              type: string
            project_status:
              type: string
            project_priority:
              type: string
            statistics:
              type: object
              properties:
                tasks:
                  type: object
                  properties:
                    total:
                      type: integer
                    completed:
                      type: integer
                    in_progress:
                      type: integer
                    todo:
                      type: integer
                    overdue:
                      type: integer
                    assigned:
                      type: integer
                    unassigned:
                      type: integer
                    recent_week:
                      type: integer
                priority_breakdown:
                  type: object
                  properties:
                    high:
                      type: integer
                    medium:
                      type: integer
                    low:
                      type: integer
                time_estimation:
                  type: object
                  properties:
                    total_estimated_hours:
                      type: number
                    completed_estimated_hours:
                      type: number
                    remaining_estimated_hours:
                      type: number
                progress:
                  type: object
                  properties:
                    percentage:
                      type: number
                    completed_tasks:
                      type: integer
                    total_tasks:
                      type: integer
                team:
                  type: object
                  properties:
                    total_members:
                      type: integer
      403:
        description: Access denied
      404:
        description: Project not found
      500:
        description: Server error
    """
    current_user_id = int(get_jwt_identity())
    result, status = get_project_statistics(project_id, current_user_id)
    return jsonify(result), status
