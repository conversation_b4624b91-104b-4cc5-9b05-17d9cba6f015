from app.models import Project, User
from app.helpers.extensions import db
from app.projects.entities import ProjectEntity
from datetime import datetime
from typing import <PERSON><PERSON>


def get_all_projects(user_id: int, include_deleted: bool = False) -> <PERSON><PERSON>[dict, int]:
    """Get all projects for a user"""
    try:
        query = Project.query
        
        if not include_deleted:
            query = query.filter(Project.deleted_at.is_(None))
        
        # Get projects where user is creator or owner
        projects = query.filter(
            (Project.created_by == user_id) | (Project.user_id == user_id)
        ).all()
        
        project_entities = [ProjectEntity.from_model(project) for project in projects]
        project_dicts = [entity.to_dict() for entity in project_entities]
        
        return {
            "code": 200,
            "data": {
                "projects": project_dicts,
                "count": len(project_dicts)
            },
            "message": "Projects retrieved successfully",
            "success": True
        }, 200
    except Exception as e:
        return {"message": f"Error retrieving projects: {str(e)}"}, 500


def get_project_by_id(project_id: int, user_id: int) -> <PERSON><PERSON>[dict, int]:
    """Get a specific project by ID"""
    try:
        project = Project.query.filter(
            Project.id == project_id,
            Project.deleted_at.is_(None),
            (Project.created_by == user_id) | (Project.user_id == user_id)
        ).first()

        if not project:
            return {"message": "Project not found"}, 404

        entity = ProjectEntity.from_model(project)
        return {"project": entity.to_dict()}, 200
    except Exception as e:
        return {"message": f"Error retrieving project: {str(e)}"}, 500


def create_project(data: dict, user_id: int) -> Tuple[dict, int]:
    """Create a new project"""
    try:
        # Parse dates if provided
        start_date = None
        end_date = None

        if data.get("start_date"):
            try:
                start_date = datetime.fromisoformat(data["start_date"]).date()
            except ValueError:
                return {"message": "Invalid start_date format. Use YYYY-MM-DD"}, 400

        if data.get("end_date"):
            try:
                end_date = datetime.fromisoformat(data["end_date"]).date()
            except ValueError:
                return {"message": "Invalid end_date format. Use YYYY-MM-DD"}, 400

        # Create entity for validation
        entity = ProjectEntity(
            name=data.get("name", ""),
            description=data.get("description"),
            status=data.get("status", "active"),
            priority=data.get("priority", "medium"),
            start_date=start_date,
            end_date=end_date,
            created_by=user_id,
            user_id=data.get("user_id", user_id)  # Default to creator as owner
        )

        # Validate entity
        is_valid, error_message = entity.is_valid()
        if not is_valid:
            return {"message": error_message}, 400

        # Check if user_id exists if provided and different from creator
        if entity.user_id != user_id:
            owner = User.query.get(entity.user_id)
            if not owner:
                return {"message": "Invalid user_id: User not found"}, 400

        # Create model instance
        project = Project(
            name=entity.name,
            description=entity.description,
            status=entity.status,
            priority=entity.priority,
            start_date=entity.start_date,
            end_date=entity.end_date,
            created_by=entity.created_by,
            user_id=entity.user_id
        )

        db.session.add(project)
        db.session.commit()

        # Return created project
        created_entity = ProjectEntity.from_model(project)
        return {"message": "Project created successfully", "project": created_entity.to_dict()}, 201

    except Exception as e:
        db.session.rollback()
        return {"message": f"Error creating project: {str(e)}"}, 500


def update_project(project_id: int, data: dict, user_id: int) -> Tuple[dict, int]:
    """Update an existing project"""
    try:
        project = Project.query.filter(
            Project.id == project_id,
            Project.deleted_at.is_(None),
            (Project.created_by == user_id) | (Project.user_id == user_id)
        ).first()

        if not project:
            return {"message": "Project not found"}, 404

        # Parse dates if provided
        start_date = project.start_date
        end_date = project.end_date

        if "start_date" in data:
            if data["start_date"]:
                try:
                    start_date = datetime.fromisoformat(data["start_date"]).date()
                except ValueError:
                    return {"message": "Invalid start_date format. Use YYYY-MM-DD"}, 400
            else:
                start_date = None

        if "end_date" in data:
            if data["end_date"]:
                try:
                    end_date = datetime.fromisoformat(data["end_date"]).date()
                except ValueError:
                    return {"message": "Invalid end_date format. Use YYYY-MM-DD"}, 400
            else:
                end_date = None

        # Create entity for validation
        entity = ProjectEntity(
            id=project.id,
            name=data.get("name", project.name),
            description=data.get("description", project.description),
            status=data.get("status", project.status),
            priority=data.get("priority", project.priority),
            start_date=start_date,
            end_date=end_date,
            created_by=project.created_by,
            user_id=data.get("user_id", project.user_id)
        )

        # Validate entity
        is_valid, error_message = entity.is_valid()
        if not is_valid:
            return {"message": error_message}, 400

        # Check if user_id exists if changed
        if entity.user_id != project.user_id:
            owner = User.query.get(entity.user_id)
            if not owner:
                return {"message": "Invalid user_id: User not found"}, 400

        # Update model
        project.name = entity.name
        project.description = entity.description
        project.status = entity.status
        project.priority = entity.priority
        project.start_date = entity.start_date
        project.end_date = entity.end_date
        project.user_id = entity.user_id
        project.updated_at = datetime.utcnow()

        db.session.commit()

        # Return updated project
        updated_entity = ProjectEntity.from_model(project)
        return {"message": "Project updated successfully", "project": updated_entity.to_dict()}, 200

    except Exception as e:
        db.session.rollback()
        return {"message": f"Error updating project: {str(e)}"}, 500

def delete_project(project_id: int, user_id: int, hard_delete: bool = False) -> Tuple[dict, int]:
    """Delete a project (soft delete by default)"""
    try:
        project = Project.query.filter(
            Project.id == project_id,
            Project.deleted_at.is_(None),
            Project.created_by == user_id  # Only creator can delete
        ).first()

        if not project:
            return {"message": "Project not found or you don't have permission to delete it"}, 404

        if hard_delete:
            # Hard delete - remove from database
            db.session.delete(project)
        else:
            # Soft delete - mark as deleted
            project.deleted_at = datetime.utcnow()

        db.session.commit()

        delete_type = "permanently deleted" if hard_delete else "deleted"
        return {"message": f"Project {delete_type} successfully"}, 200

    except Exception as e:
        db.session.rollback()
        return {"message": f"Error deleting project: {str(e)}"}, 500

def restore_project(project_id: int, user_id: int) -> Tuple[dict, int]:
    """Restore a soft-deleted project"""
    try:
        project = Project.query.filter(
            Project.id == project_id,
            Project.deleted_at.is_not(None),
            Project.created_by == user_id
        ).first()

        if not project:
            return {"message": "Deleted project not found"}, 404

        project.deleted_at = None
        project.updated_at = datetime.utcnow()

        db.session.commit()

        restored_entity = ProjectEntity.from_model(project)
        return {"message": "Project restored successfully", "project": restored_entity.to_dict()}, 200

    except Exception as e:
        db.session.rollback()
        return {"message": f"Error restoring project: {str(e)}"}, 500